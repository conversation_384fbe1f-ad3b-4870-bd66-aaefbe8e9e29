import random
import csv
from collections import defaultdict

teams = [
    {"name": "Switzerland", "fifa_rank": 23, "odds": 0.033},
    {"name": "Norway", "fifa_rank": 16, "odds": 0.036},
    {"name": "Iceland", "fifa_rank": 13, "odds": 0.024},
    {"name": "Finland", "fifa_rank": 25, "odds": 0.006},
    {"name": "Spain", "fifa_rank": 2, "odds": 0.33},
    {"name": "Portugal", "fifa_rank": 22, "odds": 0.017},
    {"name": "Belgium", "fifa_rank": 20, "odds": 0.014},
    {"name": "Italy", "fifa_rank": 13, "odds": 0.037},
    {"name": "Germany", "fifa_rank": 3, "odds": 0.172},
    {"name": "Poland", "fifa_rank": 27, "odds": 0.004},
    {"name": "Denmark", "fifa_rank": 12, "odds": 0.045},
    {"name": "Sweden", "fifa_rank": 6, "odds": 0.065},
    {"name": "France", "fifa_rank": 10, "odds": 0.176},
    {"name": "England", "fifa_rank": 4, "odds": 0.2},
    {"name": "Netherlands", "fifa_rank": 10, "odds": 0.059},
    {"name": "Wales", "fifa_rank": 31, "odds": 0.004},
]

# Load head-to-head data
def load_head_to_head_data():
    """Load head-to-head data from CSV file."""
    h2h_data = {}
    team_stats = defaultdict(lambda: {"total_goals_for": 0, "total_goals_against": 0, "total_matches": 0})

    with open('head_to_head_results.csv', 'r') as file:
        reader = csv.DictReader(file)
        for row in reader:
            team1 = row['team1'].lower()
            team2 = row['team2'].lower()

            # Store head-to-head record
            key = f"{team1}_vs_{team2}"
            h2h_data[key] = {
                'wins': int(row['wins']),
                'draws': int(row['draws']),
                'losses': int(row['losses']),
                'matches_played': int(row['matches_played']),
                'avg_team1_score': float(row['avg_team1_score']),
                'avg_team2_score': float(row['avg_team2_score']),
                'total_goals_team1': int(row['total_goals_team1']),
                'total_goals_team2': int(row['total_goals_team2'])
            }

            # Accumulate team statistics
            if int(row['matches_played']) > 0:
                team_stats[team1]["total_goals_for"] += int(row['total_goals_team1'])
                team_stats[team1]["total_goals_against"] += int(row['total_goals_team2'])
                team_stats[team1]["total_matches"] += int(row['matches_played'])

                team_stats[team2]["total_goals_for"] += int(row['total_goals_team2'])
                team_stats[team2]["total_goals_against"] += int(row['total_goals_team1'])
                team_stats[team2]["total_matches"] += int(row['matches_played'])

    # Calculate averages
    team_averages = {}
    for team, stats in team_stats.items():
        if stats["total_matches"] > 0:
            team_averages[team] = {
                "avg_goals_for": stats["total_goals_for"] / stats["total_matches"],
                "avg_goals_against": stats["total_goals_against"] / stats["total_matches"],
                "total_matches": stats["total_matches"]
            }
        else:
            team_averages[team] = {
                "avg_goals_for": 1.0,  # Default values for teams with no data
                "avg_goals_against": 1.0,
                "total_matches": 0
            }

    return h2h_data, team_averages

# Load the data
head_to_head_data, team_averages = load_head_to_head_data()

# Group definitions
groups = {
    'A': [teams[0], teams[1], teams[2], teams[3]],  # Switzerland, Norway, Iceland, Finland
    'B': [teams[4], teams[5], teams[6], teams[7]],  # Spain, Portugal, Belgium, Italy
    'C': [teams[8], teams[9], teams[10], teams[11]], # Germany, Poland, Denmark, Sweden
    'D': [teams[12], teams[13], teams[14], teams[15]] # France, England, Netherlands, Wales
}

group_match_order = [
    [0, 1],  # Team 0 vs Team 1
    [2, 3],  # Team 2 vs Team 3
    [0, 2],  # Team 0 vs Team 2
    [1, 3],  # Team 1 vs Team 3
    [2, 1],  # Team 2 vs Team 1
    [3, 0],  # Team 3 vs Team 0
]

def calculate_team_strength(team):
    """
    Calculate team strength based on FIFA rank, head-to-head performance, and betting odds.
    """
    team_name = team["name"].lower()

    # FIFA rank component (higher rank = lower strength, so invert)
    fifa_strength = 1 / team["fifa_rank"] * 100

    # Head-to-head performance component
    if team_name in team_averages and team_averages[team_name]["total_matches"] > 0:
        avg_goals_for = team_averages[team_name]["avg_goals_for"]
        avg_goals_against = team_averages[team_name]["avg_goals_against"]

        # Calculate goal difference and scoring rate
        goal_diff = avg_goals_for - avg_goals_against
        scoring_rate = avg_goals_for

        # Normalize to 0-100 scale
        h2h_strength = (goal_diff + 2) * 25 + scoring_rate * 15  # Adjusted scaling
        h2h_strength = max(0, min(100, h2h_strength))
    else:
        # Default strength for teams with no head-to-head data
        h2h_strength = 50

    # Betting odds component (higher odds = higher strength)
    odds_strength = team["odds"] * 100

    # Weighted combination (emphasize FIFA rank and odds, moderate weight on h2h)
    strength = (fifa_strength * 0.4 + h2h_strength * 0.3 + odds_strength * 0.3)

    return strength

def get_head_to_head_info(team1, team2):
    """
    Get head-to-head information between two teams.
    """
    team1_name = team1["name"].lower()
    team2_name = team2["name"].lower()

    # Try both combinations
    key1 = f"{team1_name}_vs_{team2_name}"
    key2 = f"{team2_name}_vs_{team1_name}"

    if key1 in head_to_head_data:
        h2h = head_to_head_data[key1]
        return h2h, False  # False means team1 is the first team in the record
    elif key2 in head_to_head_data:
        h2h = head_to_head_data[key2]
        return h2h, True   # True means team1 is the second team in the record
    else:
        return None, False

def simulate_match(team1, team2, allow_draw=True):
    """
    Simulate a match between two teams based on their relative strengths and head-to-head data.
    Returns 1 if team1 wins, 0 if team2 wins, 0.5 if draw (when allowed).
    """
    strength1 = calculate_team_strength(team1)
    strength2 = calculate_team_strength(team2)

    # Get head-to-head information
    h2h_info, reversed_teams = get_head_to_head_info(team1, team2)

    # Calculate base probabilities
    total_strength = strength1 + strength2
    team1_base_prob = strength1 / total_strength

    # Adjust probabilities based on head-to-head data if available
    if h2h_info and h2h_info['matches_played'] > 0:
        if reversed_teams:
            # team1 is actually team2 in the h2h record
            h2h_team1_wins = h2h_info['losses']
            h2h_draws = h2h_info['draws']
            h2h_team2_wins = h2h_info['wins']
        else:
            # team1 is team1 in the h2h record
            h2h_team1_wins = h2h_info['wins']
            h2h_draws = h2h_info['draws']
            h2h_team2_wins = h2h_info['losses']

        total_h2h_matches = h2h_info['matches_played']
        h2h_team1_prob = h2h_team1_wins / total_h2h_matches
        h2h_draw_prob = h2h_draws / total_h2h_matches

        # Blend head-to-head with strength-based probability
        team1_base_prob = (team1_base_prob * 0.6 + h2h_team1_prob * 0.4)

    # Add some randomness to make it more realistic
    random_factor = random.uniform(0.8, 1.2)
    team1_base_prob *= random_factor
    team1_base_prob = max(0.05, min(0.95, team1_base_prob))

    if allow_draw:
        # In group stage, allow for draws
        if h2h_info and h2h_info['matches_played'] > 0:
            # Use head-to-head draw rate as base
            if reversed_teams:
                draw_probability = h2h_info['draws'] / h2h_info['matches_played']
            else:
                draw_probability = h2h_info['draws'] / h2h_info['matches_played']
            draw_probability = max(0.15, min(0.35, draw_probability + 0.1))  # Add some base draw chance
        else:
            # Default draw probability
            draw_probability = 0.25

            # Adjust draw probability based on team strength difference
            strength_diff = abs(strength1 - strength2)
            max_strength_diff = max(calculate_team_strength(t) for t in teams) - min(calculate_team_strength(t) for t in teams)

            # More evenly matched teams have higher draw probability
            if max_strength_diff > 0:
                draw_probability *= (1 - (strength_diff / max_strength_diff) * 0.5)

            draw_probability = max(0.15, min(0.35, draw_probability))

        # Adjust win probabilities
        remaining_prob = 1 - draw_probability
        team1_win_prob = team1_base_prob * remaining_prob

        rand = random.random()
        if rand < team1_win_prob:
            return 1  # Team1 wins
        elif rand < team1_win_prob + draw_probability:
            return 0.5  # Draw
        else:
            return 0  # Team2 wins
    else:
        # Knockout stage - no draws allowed
        return 1 if random.random() < team1_base_prob else 0

def simulate_group_stage(group_teams):
    """
    Simulate all matches in a group and return winner and runner-up.
    """
    # Initialize team stats
    team_stats = {}
    for team in group_teams:
        team_stats[team["name"]] = {"points": 0, "wins": 0, "draws": 0, "losses": 0, "goals_for": 0, "goals_against": 0}

    # Simulate all matches
    for match in group_match_order:
        team1 = group_teams[match[0]]
        team2 = group_teams[match[1]]

        result = simulate_match(team1, team2, allow_draw=True)

        # Get head-to-head info for more realistic scoring
        h2h_info, reversed_teams = get_head_to_head_info(team1, team2)

        if result == 1:  # team1 wins
            team_stats[team1["name"]]["points"] += 3
            team_stats[team1["name"]]["wins"] += 1
            team_stats[team2["name"]]["losses"] += 1

            # Simulate goals based on head-to-head data if available
            if h2h_info and h2h_info['matches_played'] > 0:
                if reversed_teams:
                    avg_goals_team1 = h2h_info['avg_team2_score']
                    avg_goals_team2 = h2h_info['avg_team1_score']
                else:
                    avg_goals_team1 = h2h_info['avg_team1_score']
                    avg_goals_team2 = h2h_info['avg_team2_score']

                # Add some variation around the average
                goals_team1 = max(1, int(avg_goals_team1 + random.uniform(-0.5, 1.5)))
                goals_team2 = max(0, int(avg_goals_team2 + random.uniform(-0.5, 0.5)))
                goals_team2 = min(goals_team2, goals_team1 - 1)  # Ensure team1 wins
            else:
                # Default goal simulation
                goals_team1 = random.randint(1, 4)
                goals_team2 = random.randint(0, goals_team1 - 1)

            team_stats[team1["name"]]["goals_for"] += goals_team1
            team_stats[team1["name"]]["goals_against"] += goals_team2
            team_stats[team2["name"]]["goals_for"] += goals_team2
            team_stats[team2["name"]]["goals_against"] += goals_team1

        elif result == 0.5:  # draw
            team_stats[team1["name"]]["points"] += 1
            team_stats[team2["name"]]["points"] += 1
            team_stats[team1["name"]]["draws"] += 1
            team_stats[team2["name"]]["draws"] += 1

            # Simulate goals in draw
            if h2h_info and h2h_info['matches_played'] > 0:
                if reversed_teams:
                    avg_goals_team1 = h2h_info['avg_team2_score']
                    avg_goals_team2 = h2h_info['avg_team1_score']
                else:
                    avg_goals_team1 = h2h_info['avg_team1_score']
                    avg_goals_team2 = h2h_info['avg_team2_score']

                goals = max(0, int((avg_goals_team1 + avg_goals_team2) / 2 + random.uniform(-0.5, 1.0)))
            else:
                goals = random.randint(0, 3)

            team_stats[team1["name"]]["goals_for"] += goals
            team_stats[team1["name"]]["goals_against"] += goals
            team_stats[team2["name"]]["goals_for"] += goals
            team_stats[team2["name"]]["goals_against"] += goals

        else:  # team2 wins
            team_stats[team2["name"]]["points"] += 3
            team_stats[team2["name"]]["wins"] += 1
            team_stats[team1["name"]]["losses"] += 1

            # Simulate goals based on head-to-head data if available
            if h2h_info and h2h_info['matches_played'] > 0:
                if reversed_teams:
                    avg_goals_team1 = h2h_info['avg_team2_score']
                    avg_goals_team2 = h2h_info['avg_team1_score']
                else:
                    avg_goals_team1 = h2h_info['avg_team1_score']
                    avg_goals_team2 = h2h_info['avg_team2_score']

                # Add some variation around the average
                goals_team2 = max(1, int(avg_goals_team2 + random.uniform(-0.5, 1.5)))
                goals_team1 = max(0, int(avg_goals_team1 + random.uniform(-0.5, 0.5)))
                goals_team1 = min(goals_team1, goals_team2 - 1)  # Ensure team2 wins
            else:
                # Default goal simulation
                goals_team2 = random.randint(1, 4)
                goals_team1 = random.randint(0, goals_team2 - 1)

            team_stats[team1["name"]]["goals_for"] += goals_team1
            team_stats[team1["name"]]["goals_against"] += goals_team2
            team_stats[team2["name"]]["goals_for"] += goals_team2
            team_stats[team2["name"]]["goals_against"] += goals_team1

    # Sort teams by points, then goal difference, then goals for
    sorted_teams = sorted(group_teams,
                         key=lambda t: (team_stats[t["name"]]["points"],
                                      team_stats[t["name"]]["goals_for"] - team_stats[t["name"]]["goals_against"],
                                      team_stats[t["name"]]["goals_for"]),
                         reverse=True)

    return sorted_teams[0], sorted_teams[1]  # Winner, Runner-up

def simulate_knockout_match(team1, team2):
    """
    Simulate a knockout match (no draws allowed).
    """
    return team1 if simulate_match(team1, team2, allow_draw=False) == 1 else team2

def simulate_tournament():
    """
    Simulate the entire tournament and return results.
    """
    results = {}

    # Simulate group stages
    group_winners = {}
    group_runners_up = {}

    for group_name, group_teams in groups.items():
        winner, runner_up = simulate_group_stage(group_teams)
        group_winners[group_name] = winner
        group_runners_up[group_name] = runner_up

    results['group_winners'] = group_winners
    results['group_runners_up'] = group_runners_up

    # Quarter-finals
    qf_matches = [
        (group_winners['A'], group_runners_up['B']),  # QF1
        (group_winners['C'], group_runners_up['D']),  # QF2
        (group_winners['B'], group_runners_up['A']),  # QF3
        (group_winners['D'], group_runners_up['C'])   # QF4
    ]

    qf_winners = []
    for i, (team1, team2) in enumerate(qf_matches):
        winner = simulate_knockout_match(team1, team2)
        qf_winners.append(winner)

    results['quarter_final_winners'] = qf_winners

    # Semi-finals
    sf_matches = [
        (qf_winners[0], qf_winners[1]),  # SF1
        (qf_winners[2], qf_winners[3])   # SF2
    ]

    sf_winners = []
    for team1, team2 in sf_matches:
        winner = simulate_knockout_match(team1, team2)
        sf_winners.append(winner)

    results['semi_final_winners'] = sf_winners

    # Final
    final_winner = simulate_knockout_match(sf_winners[0], sf_winners[1])
    results['tournament_winner'] = final_winner

    return results

def run_monte_carlo_simulation(num_simulations=10000):
    """
    Run Monte Carlo simulation and collect statistics.
    """
    print(f"Running {num_simulations} simulations...")

    # Initialize statistics
    stats = {
        'tournament_winners': defaultdict(int),
        'group_winners': {group: defaultdict(int) for group in groups.keys()},
        'group_runners_up': {group: defaultdict(int) for group in groups.keys()},
        'quarter_finalists': defaultdict(int),
        'semi_finalists': defaultdict(int),
        'finalists': defaultdict(int)
    }

    # Run simulations
    for i in range(num_simulations):
        if (i + 1) % 1000 == 0:
            print(f"Completed {i + 1} simulations...")

        results = simulate_tournament()

        # Record tournament winner
        winner_name = results['tournament_winner']['name']
        stats['tournament_winners'][winner_name] += 1

        # Record group stage results
        for group_name, winner in results['group_winners'].items():
            stats['group_winners'][group_name][winner['name']] += 1

        for group_name, runner_up in results['group_runners_up'].items():
            stats['group_runners_up'][group_name][runner_up['name']] += 1

        # Record knockout stage appearances
        for winner in results['quarter_final_winners']:
            stats['quarter_finalists'][winner['name']] += 1

        for winner in results['semi_final_winners']:
            stats['semi_finalists'][winner['name']] += 1
            stats['finalists'][winner['name']] += 1

        # Tournament winner also appears in final
        stats['finalists'][winner_name] += 1

    return stats, num_simulations

def display_results(stats, num_simulations):
    """
    Display the simulation results in a readable format.
    """
    print("\n" + "="*80)
    print("WOMEN'S EUROS 2025 - MONTE CARLO SIMULATION RESULTS")
    print(f"Based on {num_simulations:,} simulations")
    print("="*80)

    # Tournament winners
    print("\nTOURNAMENT WINNER PROBABILITIES:")
    print("-" * 50)
    sorted_winners = sorted(stats['tournament_winners'].items(),
                           key=lambda x: x[1], reverse=True)
    for team, wins in sorted_winners:
        percentage = (wins / num_simulations) * 100
        print(f"{team:<20} {percentage:6.2f}% ({wins:,} wins)")

    # Group stage results
    print("\nGROUP STAGE RESULTS:")
    print("-" * 50)
    for group_name in ['A', 'B', 'C', 'D']:
        print(f"\nGroup {group_name}:")
        print("  Winners:")
        sorted_winners = sorted(stats['group_winners'][group_name].items(),
                               key=lambda x: x[1], reverse=True)
        for team, wins in sorted_winners:
            percentage = (wins / num_simulations) * 100
            print(f"    {team:<20} {percentage:6.2f}%")

        print("  Runners-up:")
        sorted_runners = sorted(stats['group_runners_up'][group_name].items(),
                               key=lambda x: x[1], reverse=True)
        for team, appearances in sorted_runners:
            percentage = (appearances / num_simulations) * 100
            print(f"    {team:<20} {percentage:6.2f}%")

    # Knockout stage appearances
    print("\nKNOCKOUT STAGE APPEARANCES:")
    print("-" * 50)

    stages = [
        ('Quarter-Finals', stats['quarter_finalists']),
        ('Semi-Finals', stats['semi_finalists']),
        ('Finals', stats['finalists'])
    ]

    for stage_name, stage_stats in stages:
        print(f"\n{stage_name}:")
        sorted_teams = sorted(stage_stats.items(), key=lambda x: x[1], reverse=True)
        for team, appearances in sorted_teams[:10]:  # Top 10 teams
            percentage = (appearances / num_simulations) * 100
            print(f"  {team:<20} {percentage:6.2f}% ({appearances:,} times)")

    # Team strength analysis
    print("\nTEAM STRENGTH ANALYSIS:")
    print("-" * 50)
    print("Team                 FIFA Rank  H2H Matches  Avg Goals F/A  Betting Odds  Calculated Strength")
    print("-" * 95)

    team_strengths = []
    for team in teams:
        strength = calculate_team_strength(team)
        team_name = team["name"].lower()

        if team_name in team_averages:
            matches = team_averages[team_name]["total_matches"]
            avg_for = team_averages[team_name]["avg_goals_for"]
            avg_against = team_averages[team_name]["avg_goals_against"]
            h2h_str = f"{matches:>3} ({avg_for:.1f}/{avg_against:.1f})"
        else:
            h2h_str = "  0 (0.0/0.0)"

        team_strengths.append((team, strength, h2h_str))

    # Sort by strength
    team_strengths.sort(key=lambda x: x[1], reverse=True)

    for team, strength, h2h_str in team_strengths:
        odds_pct = team["odds"] * 100
        print(f"{team['name']:<20} {team['fifa_rank']:>9}  {h2h_str:>13}    {odds_pct:>8.1f}%      {strength:>8.2f}")

if __name__ == "__main__":
    # Run the simulation
    stats, num_sims = run_monte_carlo_simulation(10000)

    # Display results
    display_results(stats, num_sims)

    print("\n" + "="*80)
    print("Simulation complete!")
    print("="*80)